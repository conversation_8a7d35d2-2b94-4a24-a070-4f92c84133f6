<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#4B0082',
                        secondary: '#D8BFD8',
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        /* Custom dark mode styles for checkout page */
        [data-theme="dark"] body,
        body.dark-theme {
            background-color: var(--background-color) !important;
            color: var(--text-color) !important;
        }

        /* Dark mode for checkout cards */
        [data-theme="dark"] .checkout-card,
        body.dark-theme .checkout-card {
            background: var(--card-bg) !important;
            border-color: var(--border-color) !important;
            color: var(--text-color) !important;
        }

        /* Dark mode for form elements */
        [data-theme="dark"] .checkout-input,
        [data-theme="dark"] .checkout-select,
        body.dark-theme .checkout-input,
        body.dark-theme .checkout-select {
            background: var(--input-bg) !important;
            border-color: var(--input-border) !important;
            color: var(--text-color) !important;
        }

        [data-theme="dark"] .checkout-input:focus,
        [data-theme="dark"] .checkout-select:focus,
        body.dark-theme .checkout-input:focus,
        body.dark-theme .checkout-select:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2) !important;
        }

        /* Dark mode for labels */
        [data-theme="dark"] .checkout-label,
        body.dark-theme .checkout-label {
            color: var(--text-color) !important;
        }

        /* Dark mode for text elements */
        [data-theme="dark"] .checkout-text,
        body.dark-theme .checkout-text {
            color: var(--text-light) !important;
        }

        /* Dark mode for borders */
        [data-theme="dark"] .checkout-border,
        body.dark-theme .checkout-border {
            border-color: var(--border-color) !important;
        }

        /* Dark mode for hero section */
        [data-theme="dark"] .checkout-hero,
        body.dark-theme .checkout-hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, #6a1b9a 100%) !important;
        }

        /* Dark mode for footer override */
        [data-theme="dark"] .checkout-footer,
        body.dark-theme .checkout-footer {
            background: var(--footer-bg) !important;
            color: var(--footer-text) !important;
            border-top: 1px solid var(--border-color) !important;
        }

        [data-theme="dark"] .checkout-footer h3,
        [data-theme="dark"] .checkout-footer h4,
        body.dark-theme .checkout-footer h3,
        body.dark-theme .checkout-footer h4 {
            color: var(--primary-color) !important;
        }

        [data-theme="dark"] .checkout-footer p,
        body.dark-theme .checkout-footer p {
            color: var(--footer-text) !important;
            opacity: 0.9;
        }

        [data-theme="dark"] .checkout-footer a,
        body.dark-theme .checkout-footer a {
            color: var(--footer-text) !important;
            opacity: 0.85;
        }

        [data-theme="dark"] .checkout-footer a:hover,
        body.dark-theme .checkout-footer a:hover {
            color: var(--primary-color) !important;
            opacity: 1;
        }

        [data-theme="dark"] .checkout-footer .border-gray-800,
        body.dark-theme .checkout-footer .border-gray-800 {
            border-color: var(--border-color) !important;
        }

        /* Dark mode for product titles and headings */
        [data-theme="dark"] .checkout-card h2,
        [data-theme="dark"] .checkout-card h3,
        body.dark-theme .checkout-card h2,
        body.dark-theme .checkout-card h3 {
            color: var(--text-color) !important;
        }

        /* Dark mode for product titles specifically */
        [data-theme="dark"] .checkout-card .font-medium,
        body.dark-theme .checkout-card .font-medium {
            color: var(--text-color) !important;
        }

        /* Dark mode for total and pricing text */
        [data-theme="dark"] .checkout-card .font-semibold,
        body.dark-theme .checkout-card .font-semibold {
            color: var(--text-color) !important;
        }

        /* Dark mode for section background */
        [data-theme="dark"] .checkout-section,
        body.dark-theme .checkout-section {
            background: var(--section-bg) !important;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .modal-close {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .modal-close:hover {
            color: #000;
        }

        .communication-option {
            display: flex;
            align-items: center;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .communication-option:hover {
            border-color: #4B0082;
            background-color: #f8f9fa;
        }

        .communication-option.selected {
            border-color: #4B0082;
            background-color: #f3f0ff;
        }

        .communication-option i {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 2rem;
            text-align: center;
        }

        .email-icon { color: #dc2626; }
        .whatsapp-icon { color: #16a34a; }
        .telegram-icon { color: #0ea5e9; }

        /* Dark mode for modal */
        [data-theme="dark"] .modal-content,
        body.dark-theme .modal-content {
            background-color: var(--card-bg) !important;
            color: var(--text-color) !important;
        }

        [data-theme="dark"] .communication-option,
        body.dark-theme .communication-option {
            border-color: var(--border-color) !important;
            background-color: var(--input-bg) !important;
            color: var(--text-color) !important;
        }

        [data-theme="dark"] .communication-option:hover,
        body.dark-theme .communication-option:hover {
            border-color: #4B0082 !important;
            background-color: var(--hover-bg) !important;
        }

        /* Dark mode for select options */
        [data-theme="dark"] .checkout-select option,
        body.dark-theme .checkout-select option {
            background: var(--input-bg) !important;
            color: var(--text-color) !important;
        }

        /* Override Tailwind's hardcoded colors in dark mode */
        [data-theme="dark"] .text-gray-600,
        [data-theme="dark"] .text-gray-700,
        body.dark-theme .text-gray-600,
        body.dark-theme .text-gray-700 {
            color: var(--text-light) !important;
        }

        /* Dark mode button hover states */
        [data-theme="dark"] .bg-primary:hover,
        body.dark-theme .bg-primary:hover {
            background-color: #6a1b9a !important;
        }
    </style>
</head>
<body class="checkout-body" style="background-color: var(--background-color); color: var(--text-color);"
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="150" height="40" viewBox="0 0 150 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="16" font-weight="700" fill="url(#logoGradient)">The Project Faith</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="checkout-hero bg-gradient-to-r from-primary to-secondary pt-24 pb-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold text-white mb-4">Checkout</h1>
            <p class="text-white/80">Complete your purchase securely</p>
        </div>
    </section>

    <!-- Checkout Section -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Checkout Form -->
                <div class="lg:col-span-2 space-y-8">
                    <form id="checkoutForm">
                        <!-- Shipping Information -->
                        <div class="checkout-card bg-white rounded-2xl shadow-lg p-6">
                            <h2 class="text-2xl font-semibold text-primary mb-6">Shipping Information</h2>
                            <div class="space-y-6">
                                <div>
                                    <label class="checkout-label block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                    <input type="text" id="customerName" class="checkout-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary transition-colors" required>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="checkout-label block text-sm font-medium text-gray-700 mb-2">Email</label>
                                        <input type="email" id="customerEmail" class="checkout-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary transition-colors" required>
                                    </div>
                                    <div>
                                        <label class="checkout-label block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                        <input type="tel" id="customerPhone" class="checkout-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary transition-colors" required>
                                    </div>
                                </div>
                                <div>
                                    <label class="checkout-label block text-sm font-medium text-gray-700 mb-2">Country</label>
                                    <select id="customerCountry" class="checkout-select w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary transition-colors" required>
                                        <option value="">Select Country</option>
                                        <option value="US">United States</option>
                                        <option value="CA">Canada</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="AU">Australia</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="checkout-label block text-sm font-medium text-gray-700 mb-2">City</label>
                                    <input type="text" id="customerCity" class="checkout-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary transition-colors" required>
                                </div>
                                <div>
                                    <label class="checkout-label block text-sm font-medium text-gray-700 mb-2">Address</label>
                                    <input type="text" id="customerAddress" class="checkout-input w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-primary transition-colors" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="checkout-card bg-white rounded-2xl shadow-lg p-6 sticky top-24">
                        <h2 class="text-2xl font-semibold text-primary mb-6">Order Summary</h2>
                        <div class="space-y-4 mb-6" id="orderItems">
                            <!-- Order items will be loaded dynamically -->
                        </div>
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between checkout-text text-gray-600">
                                <span>Subtotal</span>
                                <span id="subtotal">$0.00</span>
                            </div>
                            <div class="flex justify-between checkout-text text-gray-600">
                                <span>Shipping</span>
                                <span id="shipping">$5.00</span>
                            </div>
                            <div class="flex justify-between checkout-text text-gray-600">
                                <span>Tax</span>
                                <span id="tax">$0.00</span>
                            </div>
                            <div class="flex justify-between text-lg font-semibold pt-3 checkout-border border-t border-gray-200">
                                <span>Total</span>
                                <span id="orderTotal">$0.00</span>
                            </div>
                        </div>
                        <button type="button" id="placeOrderBtn" class="w-full bg-primary text-white py-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                            Place Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Communication Method Modal -->
    <div id="communicationModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeCommunicationModal()">&times;</span>
            <h2 class="text-2xl font-semibold text-primary mb-6">Choose Communication Method</h2>
            <p class="text-gray-600 mb-6">How would you like to receive your order confirmation and details?</p>

            <div class="space-y-4">
                <div class="communication-option" data-method="email" onclick="selectCommunicationMethod('email')">
                    <i class="fas fa-envelope email-icon"></i>
                    <div>
                        <h3 class="font-semibold">Email</h3>
                        <p class="text-sm text-gray-600">Receive order details via email</p>
                    </div>
                </div>

                <div class="communication-option" data-method="whatsapp" onclick="selectCommunicationMethod('whatsapp')">
                    <i class="fab fa-whatsapp whatsapp-icon"></i>
                    <div>
                        <h3 class="font-semibold">WhatsApp</h3>
                        <p class="text-sm text-gray-600">Get order updates on WhatsApp</p>
                    </div>
                </div>

                <div class="communication-option" data-method="telegram" onclick="selectCommunicationMethod('telegram')">
                    <i class="fab fa-telegram telegram-icon"></i>
                    <div>
                        <h3 class="font-semibold">Telegram</h3>
                        <p class="text-sm text-gray-600">Receive notifications via Telegram</p>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex gap-4">
                <button onclick="closeCommunicationModal()" class="flex-1 px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button id="confirmOrderBtn" onclick="confirmOrder()" class="flex-1 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors" disabled>
                    Confirm Order
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="checkout-footer bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">About The Project Faith</h3>
                    <p class="text-gray-400">Your destination for modern fashion and timeless style. We bring you the latest trends with quality and comfort.</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="products.html" class="text-gray-400 hover:text-white transition-colors">Products</a></li>
                        <li><a href="sale.html" class="text-gray-400 hover:text-white transition-colors">Sale</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Customer Service</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Shipping Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Returns & Exchanges</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">FAQs</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Size Guide</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Connect With Us</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 VAITH. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadOrderSummary();
            setupCheckoutHandlers();
        });

        function loadOrderSummary() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const orderItemsContainer = document.getElementById('orderItems');

            if (cart.length === 0) {
                orderItemsContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-gray-600">Your cart is empty</p>
                        <a href="products.html" class="text-primary hover:underline">Continue Shopping</a>
                    </div>
                `;
                return;
            }

            // Render cart items
            orderItemsContainer.innerHTML = cart.map(item => `
                <div class="flex gap-4 pb-4 checkout-border border-b border-gray-200">
                    <img src="${item.image}" alt="${item.title}" class="w-20 h-20 object-cover rounded-lg">
                    <div class="flex-1">
                        <h3 class="font-medium checkout-text">${item.title}</h3>
                        <p class="text-sm checkout-text text-gray-600">Qty: ${item.quantity}</p>
                        <p class="text-primary font-semibold">$${(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                </div>
            `).join('');

            // Calculate totals
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const shipping = subtotal > 50 ? 0 : 5.00; // Free shipping over $50
            const taxRate = 0.08; // 8% tax
            const tax = subtotal * taxRate;
            const total = subtotal + shipping + tax;

            // Update totals
            document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('shipping').textContent = `$${shipping.toFixed(2)}`;
            document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('orderTotal').textContent = `$${total.toFixed(2)}`;
        }

        function setupCheckoutHandlers() {
            const placeOrderBtn = document.getElementById('placeOrderBtn');
            const checkoutForm = document.getElementById('checkoutForm');

            placeOrderBtn.addEventListener('click', function() {
                if (checkoutForm.checkValidity()) {
                    showCommunicationModal();
                } else {
                    checkoutForm.reportValidity();
                }
            });
        }

        let selectedCommunicationMethod = null;

        function showCommunicationModal() {
            document.getElementById('communicationModal').style.display = 'block';
            selectedCommunicationMethod = null;
            document.getElementById('confirmOrderBtn').disabled = true;

            // Reset all selections
            document.querySelectorAll('.communication-option').forEach(option => {
                option.classList.remove('selected');
            });
        }

        function closeCommunicationModal() {
            document.getElementById('communicationModal').style.display = 'none';
        }

        function selectCommunicationMethod(method) {
            selectedCommunicationMethod = method;

            // Remove selected class from all options
            document.querySelectorAll('.communication-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to chosen option
            document.querySelector(`[data-method="${method}"]`).classList.add('selected');

            // Enable confirm button
            document.getElementById('confirmOrderBtn').disabled = false;
        }

        function confirmOrder() {
            if (selectedCommunicationMethod) {
                closeCommunicationModal();
                processOrder(selectedCommunicationMethod);
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('communicationModal');
            if (event.target === modal) {
                closeCommunicationModal();
            }
        }

        function processOrder(communicationMethod) {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];

            if (cart.length === 0) {
                alert('Your cart is empty');
                return;
            }

            // Get form data
            const customerName = document.getElementById('customerName').value;
            const customerEmail = document.getElementById('customerEmail').value;
            const customerPhone = document.getElementById('customerPhone').value;
            const address = document.getElementById('customerAddress').value;
            const city = document.getElementById('customerCity').value;
            const country = document.getElementById('customerCountry').value;

            const shippingAddress = `${address}, ${city}, ${country}`;

            // Calculate total
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const shipping = subtotal > 50 ? 0 : 5.00;
            const tax = subtotal * 0.08;
            const total = subtotal + shipping + tax;

            // Prepare order data
            const orderData = {
                customerName: customerName,
                customerEmail: customerEmail,
                customerPhone: customerPhone,
                shippingAddress: shippingAddress,
                total: total,
                communicationMethod: communicationMethod,
                items: cart.map(item => ({
                    id: item.id,
                    name: item.title,
                    price: item.price,
                    quantity: item.quantity,
                    image: item.image
                }))
            };

            // Create order in admin system
            try {
                const order = adminManager.createOrder(orderData);

                // Clear cart
                localStorage.removeItem('cart');

                // Handle different communication methods
                handleOrderCommunication(order, orderData, communicationMethod);

            } catch (error) {
                console.error('Error creating order:', error);
                alert('There was an error processing your order. Please try again.');
            }
        }

        function handleOrderCommunication(order, orderData, method) {
            const orderDetails = formatOrderDetails(order, orderData);

            switch(method) {
                case 'email':
                    sendOrderByEmail(orderDetails, orderData.customerEmail);
                    break;
                case 'whatsapp':
                    sendOrderByWhatsApp(orderDetails, orderData.customerPhone);
                    break;
                case 'telegram':
                    sendOrderByTelegram(orderDetails);
                    break;
            }
        }

        function formatOrderDetails(order, orderData) {
            const itemsList = orderData.items.map(item =>
                `${item.name} - Qty: ${item.quantity} - $${(item.price * item.quantity).toFixed(2)}`
            ).join('\n');

            return `
Order ID: ${order.id}
Customer: ${orderData.customerName}
Email: ${orderData.customerEmail}
Phone: ${orderData.customerPhone}
Shipping Address: ${orderData.shippingAddress}

Items:
${itemsList}

Total: $${orderData.total.toFixed(2)}
            `.trim();
        }

        function sendOrderByEmail(orderDetails, email) {
            const subject = encodeURIComponent('Order Confirmation - VAITH');
            const body = encodeURIComponent(orderDetails);
            const mailtoLink = `mailto:${email}?subject=${subject}&body=${body}`;

            window.open(mailtoLink, '_blank');
            showSuccessMessage('Order details will be sent via email');
        }

        function sendOrderByWhatsApp(orderDetails, phone) {
            const message = encodeURIComponent(`Order Confirmation:\n\n${orderDetails}`);
            const whatsappLink = `https://wa.me/${phone.replace(/\D/g, '')}?text=${message}`;

            window.open(whatsappLink, '_blank');
            showSuccessMessage('Order details will be sent via WhatsApp');
        }

        function sendOrderByTelegram(orderDetails) {
            const message = encodeURIComponent(`Order Confirmation:\n\n${orderDetails}`);
            const telegramLink = `https://t.me/share/url?text=${message}`;

            window.open(telegramLink, '_blank');
            showSuccessMessage('Order details will be sent via Telegram');
        }

        function showSuccessMessage(message) {
            alert(`Order placed successfully! ${message}`);
            // Redirect to home page after a short delay
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }
    </script>
</body>
</html> 